import json
import os
import time
import uuid
import threading
from typing import Any, Async<PERSON><PERSON>ator, Dict, List, Optional

import httpx
from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

# Configuration
GAMMA_API_IMAGE_GENERATE_URL = "https://api.gamma.app/media/images/generate"

# Gamma image models list (from testChat.py)
GAMMA_MODELS_LIST = [
    "dall-e-3",
    "flux-1-quick", 
    "imagen-3-flash",
    "luma-photon-flash-1",
    "flux-kontext-pro",
    "flux-1-pro",
    "ideogram-v3-turbo",
    "imagen-3-pro",
    "leonardo-phoenix",
    "luma-photon-1",
    "flux-kontext-max",
    "flux-1-ultra",
    "gpt-image-1-medium",
    "gpt-image-1-low",
    "ideogram-v3",
    "recraft-v3",
    "recraft-v3-svg"
]

# Global variables for client API keys and Gamma cookies
VALID_CLIENT_KEYS: set = set()
GAMMA_COOKIES: list = []
GAMMA_WORKSPACE_IDS: list = []
current_gamma_cookie_index: int = 0
cookie_rotation_lock = threading.Lock()

# Pydantic Models
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    stream: bool = False
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None

class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str

class ModelList(BaseModel):
    object: str = "list"
    data: List[ModelInfo]

class ChatCompletionChoice(BaseModel):
    message: ChatMessage
    index: int = 0
    finish_reason: str = "stop"

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int] = Field(default_factory=lambda: {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0})

# FastAPI App
app = FastAPI(title="Gamma Image Generation OpenAI API Adapter")
security = HTTPBearer(auto_error=False)

def load_client_api_keys():
    """Load client API keys from client_api_keys.json"""
    global VALID_CLIENT_KEYS
    try:
        with open("client_api_keys.json", "r", encoding="utf-8") as f:
            keys = json.load(f)
            if not isinstance(keys, list):
                print("Warning: client_api_keys.json should contain a list of keys.")
                VALID_CLIENT_KEYS = set()
                return
            VALID_CLIENT_KEYS = set(keys)
            print(f"Successfully loaded {len(VALID_CLIENT_KEYS)} client API keys.")
    except FileNotFoundError:
        print("Error: client_api_keys.json not found. Client authentication will fail.")
        VALID_CLIENT_KEYS = set()
    except Exception as e:
        print(f"Error loading client_api_keys.json: {e}")
        VALID_CLIENT_KEYS = set()

def load_gamma_cookies():
    """Load Gamma cookies and workspace IDs from gamma_cookies.txt"""
    global GAMMA_COOKIES, GAMMA_WORKSPACE_IDS
    try:
        with open("gamma_cookies.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
            loaded_cookies = []
            loaded_workspace_ids = []
            
            for line in lines:
                stripped_line = line.strip()
                if stripped_line and not stripped_line.startswith("#"):
                    # Parse format: workspaceId----cookie_content
                    if "----" in stripped_line:
                        parts = stripped_line.split("----", 1)
                        if len(parts) == 2:
                            workspace_id = parts[0].strip()
                            cookie_content = parts[1].strip()
                            if workspace_id and cookie_content:
                                loaded_workspace_ids.append(workspace_id)
                                loaded_cookies.append(cookie_content)
                            else:
                                print(f"Warning: Invalid format in line: {stripped_line[:50]}...")
                        else:
                            print(f"Warning: Invalid format in line: {stripped_line[:50]}...")
                    else:
                        print(f"Warning: Missing '----' separator in line: {stripped_line[:50]}...")
            
            # Validate that workspace IDs and cookies have matching counts
            if len(loaded_workspace_ids) != len(loaded_cookies):
                print(f"Error: Mismatch between workspace IDs ({len(loaded_workspace_ids)}) and cookies ({len(loaded_cookies)})")
                GAMMA_WORKSPACE_IDS = []
                GAMMA_COOKIES = []
                return
            
            GAMMA_WORKSPACE_IDS = loaded_workspace_ids
            GAMMA_COOKIES = loaded_cookies
            
            if GAMMA_COOKIES and GAMMA_WORKSPACE_IDS:
                print(f"Successfully loaded {len(GAMMA_COOKIES)} Gamma cookies and workspace IDs from gamma_cookies.txt.")
            else:
                print("Warning: gamma_cookies.txt was found, but no valid cookies were loaded. Gamma API calls may fail.")

    except FileNotFoundError:
        print("Error: gamma_cookies.txt not found. Gamma API calls will fail.")
        GAMMA_COOKIES = []
        GAMMA_WORKSPACE_IDS = []
    except Exception as e:
        print(f"Error loading gamma_cookies.txt: {e}")
        GAMMA_COOKIES = []
        GAMMA_WORKSPACE_IDS = []

async def authenticate_client(auth: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """Authenticate client based on API key in Authorization header"""
    if not VALID_CLIENT_KEYS:
        print("Critical: No client API keys configured. Denying all requests.")
        raise HTTPException(status_code=503, detail="Service unavailable: Client API keys not configured on server.")
    
    if not auth or not auth.credentials:
        raise HTTPException(
            status_code=401,
            detail="API key required in Authorization header.",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if auth.credentials not in VALID_CLIENT_KEYS:
        raise HTTPException(status_code=403, detail="Invalid client API key.")

def get_next_gamma_cookie() -> tuple[str, str]:
    """Get the next Gamma workspace ID and cookie using round-robin"""
    global current_gamma_cookie_index
    
    if not GAMMA_COOKIES or not GAMMA_WORKSPACE_IDS:
        raise HTTPException(status_code=503, detail="Service unavailable: No Gamma cookies configured on server.")
    
    if len(GAMMA_COOKIES) != len(GAMMA_WORKSPACE_IDS):
        raise HTTPException(status_code=503, detail="Service unavailable: Gamma cookies and workspace IDs mismatch.")
    
    with cookie_rotation_lock:
        workspace_id = GAMMA_WORKSPACE_IDS[current_gamma_cookie_index]
        cookie_to_use = GAMMA_COOKIES[current_gamma_cookie_index]
        current_gamma_cookie_index = (current_gamma_cookie_index + 1) % len(GAMMA_COOKIES)
    
    return workspace_id, cookie_to_use

@app.on_event("startup")
async def startup():
    """应用启动时初始化配置"""
    print("Starting Gamma Image Generation OpenAI API Adapter server...")
    load_client_api_keys()
    load_gamma_cookies()
    print("Server initialization completed.")

@app.get("/v1/models", response_model=ModelList)
async def list_v1_models(_: None = Depends(authenticate_client)):
    """List available models - authenticated"""
    return get_models_list_response()

@app.get("/models", response_model=ModelList)
async def list_models_no_auth():
    """List available models without authentication - for client compatibility"""
    return get_models_list_response()

def get_models_list_response() -> ModelList:
    """Helper to construct ModelList response"""
    model_info_objects = []
    created_timestamp = int(time.time())
    
    for model_id in GAMMA_MODELS_LIST:
        model_info_objects.append(
            ModelInfo(
                id=model_id,
                created=created_timestamp,
                owned_by="gamma"
            )
        )
    
    return ModelList(data=model_info_objects)

@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest,
    _: None = Depends(authenticate_client)
):
    """Create image generation using Gamma backend"""
    if request.model not in GAMMA_MODELS_LIST:
        raise HTTPException(status_code=404, detail=f"Model '{request.model}' not found or not available.")
    
    workspace_id, gamma_cookie = get_next_gamma_cookie()
    
    if not request.messages:
        raise HTTPException(status_code=400, detail="No messages provided in the request.")

    # Extract prompt from last user message
    user_messages = [msg for msg in request.messages if msg.role == "user"]
    if not user_messages:
        raise HTTPException(status_code=400, detail="No user messages found in the request.")
    
    prompt_content = user_messages[-1].content
    if not prompt_content.strip():
        raise HTTPException(status_code=400, detail="Empty prompt provided.")

    # Construct Gamma payload with dynamic workspace ID
    gamma_payload = {
        "model": request.model,
        "interactionId": f"g2api-{uuid.uuid4().hex}",
        "workspaceId": workspace_id,
        "prompt": prompt_content,
        "stylePreset": "None",
        "stylePrompt": "",
        "aspectRatio": "square",
        "count": 1,
        "context": "dashboard",
        "fallbackModel": "ideogram-v3-turbo",
    }
    
    # Set headers for Gamma API
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Content-Type": "application/json",
        "origin": "https://gamma.app",
        "referer": "https://gamma.app/",
        "Cookie": gamma_cookie,
    }
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(GAMMA_API_IMAGE_GENERATE_URL, json=gamma_payload, headers=headers)
            response.raise_for_status()
            response_json = response.json()
            
            # Extract image URL from Gamma response
            if not response_json or not isinstance(response_json, list) or len(response_json) == 0:
                raise HTTPException(status_code=502, detail="Invalid response from Gamma API: empty or malformed response")
            
            first_image = response_json[0]
            if not isinstance(first_image, dict) or 'attrs' not in first_image or 'src' not in first_image['attrs']:
                raise HTTPException(status_code=502, detail="Invalid response from Gamma API: missing image URL")
            
            image_url = first_image['attrs']['src']
            markdown_image = f"![image]({image_url})"
            
            # Return non-streaming response (ignore stream parameter)
            return ChatCompletionResponse(
                model=request.model,
                choices=[ChatCompletionChoice(message=ChatMessage(role="assistant", content=markdown_image))]
            )

    except httpx.HTTPStatusError as e:
        error_text = e.response.text
        try:
            error_json = e.response.json()
            error_detail = error_json.get("message") or error_json.get("error", {}).get("message") or error_text
        except json.JSONDecodeError:
            error_detail = error_text
        
        print(f"Error from Gamma API ({e.response.status_code}): {error_detail}")
        raise HTTPException(status_code=e.response.status_code, detail=error_detail)
            
    except json.JSONDecodeError as e:
        error_detail = f"Error decoding JSON response from Gamma: {str(e)}"
        print(error_detail)
        raise HTTPException(status_code=502, detail=error_detail)

    except Exception as e:
        error_detail = f"Internal server error during Gamma call: {str(e)}"
        import traceback
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=error_detail)

if __name__ == "__main__":
    import uvicorn
    
    # Load configurations for startup info
    load_client_api_keys()
    load_gamma_cookies()

    print("\n--- Gamma Image Generation OpenAI API Adapter ---")
    print("Endpoints:")
    print("  GET  /v1/models (Client API Key Auth)")
    print("  GET  /models (No Auth - for compatibility)")
    print("  POST /v1/chat/completions (Client API Key Auth)")
    
    client_keys_preview = list(VALID_CLIENT_KEYS)
    print(f"\nClient API Key(s) for this proxy: {client_keys_preview if client_keys_preview else 'No client API keys loaded. Check client_api_keys.json.'}")
    
    if GAMMA_COOKIES and GAMMA_WORKSPACE_IDS:
        print(f"Gamma Cookies loaded: {len(GAMMA_COOKIES)}. First workspace ID: {GAMMA_WORKSPACE_IDS[0]}")
        print(f"First cookie preview: {GAMMA_COOKIES[0][:50]}...")
    else:
        print("Gamma Cookies: None loaded. Check gamma_cookies.txt.")
        
    print(f"Available Gamma image models: {len(GAMMA_MODELS_LIST)}")
    print("------------------------------------")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)